<template>
  <div class="mobile-person-center">
    <!-- 用户头像和基本信息 -->
    <div class="user-header">
      <div class="user-avatar">
        <n-avatar
          :size="80"
          class="font-medium"
          style="background-color: #66a80f; font-size: 2.4em"
          :src="userInfo.avatar || undefined"
          :fallback-src="'/default-avatar.png'"
          round
        >
          {{ userInfo.nickname?.charAt(0) || userInfo.username?.charAt(0) || '用' }}
        </n-avatar>
      </div>
      <div class="user-basic-info">
        <h2 class="user-name">{{ userInfo.nickname || userInfo.username }}</h2>
        <p class="user-id">工号: {{ userInfo.username }}</p>
        <div class="user-dept-wrapper" v-if="curDeptName">
          <div class="user-dept">
            <n-icon :size="14" class="dept-icon">
              <BusinessOutline />
            </n-icon>
            <span>{{ curDeptName }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能菜单列表 -->
    <div class="menu-container">
      <div class="menu-list">
        <div
          v-for="item in menuItems"
          :key="item.key"
          class="menu-item"
          :class="{ active: curKey === item.key }"
          @click="handleMenuClick(item)"
        >
          <div class="menu-item-left">
            <div class="menu-icon-wrapper" :style="{ '--icon-color': item.color }">
              <n-icon :size="22" class="menu-icon">
                <component :is="item.icon" />
              </n-icon>
            </div>
            <div class="menu-content">
              <div class="menu-title">{{ item.label }}</div>
              <div class="menu-desc">{{ item.description }}</div>
            </div>
          </div>
          <div class="menu-arrow-wrapper">
            <n-icon :size="16" class="menu-arrow">
              <chevron-forward-outline />
            </n-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area" v-if="curKey">
      <!-- 个人信息 -->
      <div v-if="curKey === 'user'" class="content-wrapper">
        <PcUserInfoMob :empId="userInfo.empId" @back="handleBack" />
      </div>

      <!-- 个人签章 -->
      <div v-if="curKey === 'sign'" class="content-wrapper">
        <PcUserSignMob @back="handleBack" />
      </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-bottom"></div>

    <!-- 科室切换弹窗 -->
    <n-modal
      v-model:show="showChoseDept"
      preset="dialog"
      title="选择使用科室"
      positive-text="确认"
      negative-text="取消"
      @positive-click="confirmDept"
      @negative-click="showChoseDept = false"
    >
      <div class="dept-select-container">
        <n-select v-model:value="curDept" :options="deptOptions" placeholder="请选择科室" filterable clearable />
      </div>
    </n-modal>

    <!-- 密码修改弹窗 -->
    <n-modal
      v-model:show="showModifyPwd"
      preset="card"
      title="修改密码"
      class="mobile-password-modal"
      :style="{ width: '90%', maxWidth: '400px' }"
      :mask-closable="false"
    >
      <n-alert :type="errorTip ? 'error' : 'info'" style="margin-bottom: 15px; white-space: pre-line">
        {{
          errorTip +
          (errorTip ? '\n' : '') +
          '密码必须包含大小写字母、数字、特殊字符中至少3种且长度不小于8，不能以&*()|结尾'
        }}
      </n-alert>

      <n-form ref="formRef" :model="passwordForm" :rules="passwordRules" label-placement="top" class="password-form">
        <n-form-item path="oldPassword" label="旧密码">
          <n-input
            v-model:value="passwordForm.oldPassword"
            type="password"
            placeholder="请输入旧密码"
            show-password-on="click"
            size="large"
          />
        </n-form-item>

        <n-form-item path="password" label="新密码">
          <n-input
            v-model:value="passwordForm.password"
            type="password"
            placeholder="请输入新密码"
            show-password-on="click"
            size="large"
          />
        </n-form-item>

        <n-form-item path="rNewPwd" label="确认密码">
          <n-input
            v-model:value="passwordForm.rNewPwd"
            type="password"
            placeholder="请再次输入新密码"
            show-password-on="click"
            size="large"
          />
        </n-form-item>
      </n-form>

      <template #footer>
        <n-space justify="end">
          <n-button @click="resetPasswordForm">重置</n-button>
          <n-button @click="showModifyPwd = false">取消</n-button>
          <n-button type="primary" @click="submitPasswordForm">确认</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 关于系统弹窗 -->
    <n-modal
      v-model:show="showAbout"
      preset="card"
      title="关于系统"
      class="mobile-about-modal"
      :style="{ width: '90%', maxWidth: '400px' }"
    >
      <div class="about-content">
        <!-- 背景装饰图 -->
        <div class="about-background">
          <img src="@/assets/images/login/panel-left.png" class="background-image" alt="背景装饰" />
        </div>

        <!-- 系统Logo -->
        <div class="about-logo"> </div>

        <!-- 系统信息 -->
        <div class="about-info">
          <h2 class="system-title">中江县人民医院</h2>
          <h3 class="system-subtitle">智慧财务系统</h3>

          <div class="system-details">
            <div class="detail-item">
              <span class="detail-label">系统版本：</span>
              <span class="detail-value">v0.0.1</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">发布时间：</span>
              <span class="detail-value">2025年7月</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">技术支持：</span>
              <span class="detail-value">智慧医疗科技团队</span>
            </div>
          </div>

          <div class="system-description">
            <p>致力于为医院提供全面、高效、智能的财务管理解决方案，助力医院数字化转型升级。</p>
          </div>

          <div class="copyright">
            <p>© 2025 中江县人民医院 版权所有</p>
          </div>
        </div>
      </div>

      <template #footer>
        <n-space justify="center">
          <n-button type="primary" @click="showAbout = false">返回</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue'
  import { NAvatar, NIcon, NModal, NSelect, NForm, NFormItem, NInput, NButton, NSpace, NAlert } from 'naive-ui'
  import {
    PersonOutline,
    PencilOutline,
    ChevronForwardOutline,
    BusinessOutline,
    LogOutOutline,
    LockClosedOutline,
    InformationCircleOutline,
  } from '@vicons/ionicons5'
  import { useSysStore, useUserStore } from '@/store'
  import { userLogout, modifyDept, updatePwd, isOldPwdTrue } from '@/api/user'
  import { queryOrg } from '@/api/hrm/hrmOrg'
  import { useGlobalInit } from '@/types/common/globalInit'
  import { IRes, Option } from '@/types/common/jtypes'
  import { FormInst, FormRules } from 'naive-ui'
  import { useRouter } from 'vue-router'
  import Auth from '@/utils/auth'
  import PcUserInfoMob from './pcComponents/pcUserInfo-mob.vue'
  import PcUserSignMob from './pcComponents/pcUserSign-mob.vue'

  // 状态管理
  const sysStore = useSysStore()
  const userStore = useUserStore()
  const sysInit = useGlobalInit()
  const router = useRouter()

  // 响应式数据
  const curKey = ref('')
  const userInfo = ref({
    username: '',
    nickname: '',
    avatar: '',
    empId: -1,
  })

  // 科室切换相关
  const showChoseDept = ref(false)
  const curDept = ref('')
  const deptOptions = ref<Option[]>([])
  const curDeptName = ref('')

  // 密码修改相关
  const showModifyPwd = ref(false)
  const formRef = ref<FormInst | null>(null)
  const passwordForm = ref({
    id: '',
    username: '',
    oldPassword: '',
    password: '',
    rNewPwd: '',
  })
  const errorTip = ref('')

  // 关于页面相关
  const showAbout = ref(false)

  // Auth实例
  const auth = new Auth()

  // 菜单项配置
  const menuItems = ref([
    {
      key: 'user',
      label: '个人信息',
      description: '查看和编辑个人基本信息',
      icon: PersonOutline,
      color: '#2080f0',
    },
    {
      key: 'sign',
      label: '个人签章',
      description: '管理个人签名和印章',
      icon: PencilOutline,
      color: '#18a058',
    },
    {
      key: 'password',
      label: '修改密码',
      description: '修改登录密码',
      icon: LockClosedOutline,
      color: '#722ed1',
    },
    {
      key: 'dept',
      label: '科室切换',
      description: '切换当前使用的科室',
      icon: BusinessOutline,
      color: '#f0a020',
    },
    {
      key: 'about',
      label: '关于系统',
      description: '查看系统版本和相关信息',
      icon: InformationCircleOutline,
      color: '#13c2c2',
    },
    {
      key: 'logout',
      label: '退出登录',
      description: '安全退出当前账户',
      icon: LogOutOutline,
      color: '#d03050',
    },
  ])

  // 密码验证规则
  const passwordRules: FormRules = {
    oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
    password: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
    rNewPwd: [
      { required: true, message: '请再次输入新密码', trigger: 'blur' },
      {
        validator: (_rule: any, value: string) => {
          return value === passwordForm.value.password
        },
        message: '两次密码输入不一致',
        trigger: ['blur', 'password-input'],
      },
    ],
  }

  // 方法：设置用户信息
  const setUserInfo = () => {
    const user = userStore.getUserInfo
    if (user && Object.keys(user).length > 0) {
      userInfo.value = {
        username: user.username as string,
        nickname: user.nickname as string,
        avatar: '',
        empId: -1,
      }
      if (user.hrmUser && Object.keys(user.hrmUser).length > 0) {
        userInfo.value.avatar = user.hrmUser?.avatar as string
        userInfo.value.empId = user.hrmUser?.empId as number
      }
    }
  }

  // 方法：初始化科室数据
  const initDeptData = () => {
    const user = userStore.getUserInfo
    if (user && user.hrmUser && user.hrmUser.hrmOrgId) {
      let orgArr: string[] = [user.hrmUser.hrmOrgId]
      if (user.hrmUser.ajtOrgIds) {
        orgArr = [...user.hrmUser.ajtOrgIds.split(','), user.hrmUser.hrmOrgId]
      }

      curDept.value = sysStore.getDept ? sysStore.getDept : user.hrmUser.hrmOrgId

      if (orgArr.length > 0) {
        queryOrg({})
          .then((res: IRes) => {
            let options: Option[] = []
            if (res.data) {
              res.data.forEach((d: any) => {
                if (orgArr.includes(d.orgId)) {
                  options.push({
                    label: d.orgName,
                    value: d.orgId,
                  })
                }
              })
              deptOptions.value = options
              // 设置当前科室名称
              const currentDept = options.find(opt => opt.value === sysStore.getDept)
              if (currentDept) {
                curDeptName.value = currentDept.label
              }
            }
          })
          .catch(() => {
            window.$message?.error('获取科室信息失败')
          })
      }
    }
  }

  // 方法：处理菜单点击
  const handleMenuClick = (item: any) => {
    if (item.key === 'dept') {
      handleDeptSwitch()
    } else if (item.key === 'logout') {
      handleLogout()
    } else if (item.key === 'password') {
      handlePasswordModify()
    } else if (item.key === 'about') {
      handleAbout()
    } else {
      curKey.value = item.key
    }
  }

  // 方法：返回菜单
  const handleBack = () => {
    curKey.value = ''
  }

  // 方法：处理科室切换
  const handleDeptSwitch = () => {
    if (deptOptions.value.length > 1) {
      showChoseDept.value = true
    } else {
      window.$message?.info('当前只有一个科室，无需切换')
    }
  }

  // 方法：确认科室切换
  const confirmDept = () => {
    if (curDept.value !== sysStore.getDept) {
      sysStore.setDepts(deptOptions.value, curDept.value)
      modifyDept({ dept: curDept.value })
        .then(() => {
          sysInit.initUserInfo()
          showChoseDept.value = false
          window.$message?.success('科室切换成功')
          location.reload()
        })
        .catch(() => {
          window.$message?.error('科室切换失败')
        })
    } else {
      showChoseDept.value = false
    }
  }

  // 方法：处理退出登录
  const handleLogout = () => {
    userLogout({})
      .then(() => {
        auth.clearAll()
        localStorage.clear()
        window.$message?.success('退出成功')
        location.reload()
      })
      .catch(() => {
        window.$message?.error('退出失败')
      })
  }

  // 方法：处理密码修改
  const handlePasswordModify = () => {
    // 初始化表单数据
    const user = userStore.getUserInfo
    passwordForm.value = {
      id: user.id || '',
      username: user.username || '',
      oldPassword: '',
      password: '',
      rNewPwd: '',
    }
    errorTip.value = ''
    showModifyPwd.value = true
  }

  // 方法：重置密码表单
  const resetPasswordForm = () => {
    passwordForm.value = {
      id: '',
      username: '',
      oldPassword: '',
      password: '',
      rNewPwd: '',
    }
    errorTip.value = ''
  }

  // 方法：提交密码修改
  const submitPasswordForm = () => {
    formRef.value?.validate(errors => {
      if (!errors) {
        // 验证密码强度
        const passwordRegex =
          /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,16}$/
        if (!passwordRegex.test(passwordForm.value.password)) {
          errorTip.value = '新密码强度太弱，请重新修改'
          return
        }

        // 检查密码结尾
        const invalidEndings = ['&', '*', '(', ')', '|']
        if (invalidEndings.some(ending => passwordForm.value.password.endsWith(ending))) {
          errorTip.value = '新密码不能以&*()|结尾'
          return
        }

        // 验证旧密码
        isOldPwdTrue(passwordForm.value)
          .then(res => {
            if (res.code === 200 && res.data) {
              // 旧密码正确，提交修改
              updatePwd(passwordForm.value)
                .then(updateRes => {
                  if (updateRes.code === 200) {
                    window.$message?.success('密码修改成功，请重新登录')
                    showModifyPwd.value = false
                    // 清除登录状态并跳转到登录页
                    auth.clearAll()
                    localStorage.clear()
                    router.push({ path: '/login' })
                  } else {
                    window.$message?.error('密码修改失败')
                  }
                })
                .catch(() => {
                  window.$message?.error('密码修改失败')
                })
            } else {
              window.$message?.error('旧密码错误')
            }
          })
          .catch(() => {
            window.$message?.error('旧密码验证失败')
          })
      }
    })
  }

  // 方法：处理关于页面
  const handleAbout = () => {
    showAbout.value = true
  }

  // 生命周期
  onMounted(() => {
    setUserInfo()
    initDeptData()
  })

  // 监听系统加载完成
  watch(
    () => sysStore.$state.initOnload,
    (newVal: boolean) => {
      if (newVal) {
        setUserInfo()
        initDeptData()
      }
    }
  )

  // 监听科室变化
  watch(
    () => sysStore.$state.curDept,
    dept => {
      if (dept && deptOptions.value.length > 0) {
        const currentDept = deptOptions.value.find(opt => opt.value === dept)
        if (currentDept) {
          curDeptName.value = currentDept.label
          curDept.value = dept
        }
      }
    },
    { immediate: true }
  )
</script>

<style scoped lang="less">
  .mobile-person-center {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding-bottom: env(safe-area-inset-bottom);
  }

  .user-header {
    background: url(@/assets/images/login/panel-left.jpg) center/cover no-repeat;
    padding: 40px 20px 30px;
    height: 290px;

    display: flex;
    align-items: center;
    gap: 20px;
    color: white;
    padding-top: calc(40px + env(safe-area-inset-top));
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      z-index: 0;
    }
  }

  .user-avatar {
    flex-shrink: 0;
    position: relative;
    z-index: 1;
  }

  .user-basic-info {
    flex: 1;
    position: relative;
    z-index: 1;
  }

  .user-name {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .user-id {
    font-size: 14px;
    margin: 0 0 8px 0;
    opacity: 0.9;
    color: white;
  }

  .user-dept-wrapper {
    margin-top: 8px;
  }

  .user-dept {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: white;
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 15px;
    backdrop-filter: blur(5px);
  }

  .dept-icon {
    opacity: 0.9;
  }

  .menu-container {
    padding: 20px 16px 0;
  }

  .menu-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .menu-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
    -webkit-tap-highlight-color: transparent;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f8f9fa;
    }

    &:active {
      background-color: #e9ecef;
    }

    &.active {
      background-color: #e6f7ff;
      border-left: 4px solid #1890ff;
    }
  }

  .menu-item-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
  }

  .menu-icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: var(--icon-color, #2080f0);
    flex-shrink: 0;
  }

  .menu-icon {
    flex-shrink: 0;
  }

  .menu-content {
    flex: 1;
  }

  .menu-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  .menu-desc {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
  }

  .menu-arrow-wrapper {
    padding: 4px;
  }

  .menu-arrow {
    color: #ccc;
    flex-shrink: 0;
  }

  .content-area {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 1000;
  }

  .content-wrapper {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .safe-bottom {
    height: env(safe-area-inset-bottom);
  }

  /* 响应式适配 */
  @media (max-width: 480px) {
    .user-header {
      padding: 30px 16px 20px;
      gap: 16px;
    }

    .user-name {
      font-size: 20px;
    }

    .menu-container {
      padding: 16px 12px 0;
    }

    .menu-item {
      padding: 16px;
    }

    .menu-icon-wrapper {
      width: 36px;
      height: 36px;
      border-radius: 8px;
    }

    .menu-title {
      font-size: 15px;
    }

    .menu-desc {
      font-size: 12px;
    }
  }

  /* 科室选择弹窗样式 */
  .dept-select-container {
    padding: 16px 0;
  }

  /* 密码修改弹窗样式 */
  .mobile-password-modal {
    margin: 20px auto;
  }

  .password-form {
    margin-top: 16px;
  }

  .password-form .n-form-item {
    margin-bottom: 20px;
  }

  /* 关于系统弹窗样式 */
  .mobile-about-modal {
    margin: 20px auto;
    overflow: hidden;
  }

  .about-content {
    text-align: center;
    padding: 20px 0;
    position: relative;
    overflow: hidden;
  }

  .about-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    opacity: 0.6;
    pointer-events: none;
  }

  .background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .about-logo {
  }

  .form-logo {
    width: 120px;
    height: auto;
    max-width: 100%;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .about-info {
    margin-top: 16px;
    position: relative;
    z-index: 1;
  }

  .system-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    line-height: 1.3;
  }

  .system-subtitle {
    font-size: 18px;
    font-weight: 500;
    color: #2080f0;
    margin: 0 0 24px 0;
    line-height: 1.3;
  }

  .system-details {
    background: rgba(248, 249, 250, 0.95);
    border-radius: 12px;
    padding: 16px;
    margin: 20px 0;
    text-align: left;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e8e8e8;
  }

  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  .detail-value {
    font-size: 14px;
    color: #333;
    font-weight: 400;
  }

  .system-description {
    margin: 20px 0;
    padding: 16px;
    background: linear-gradient(135deg, rgba(230, 247, 255, 0.9), rgba(240, 249, 255, 0.9));
    border-radius: 12px;
    border-left: 4px solid #2080f0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .system-description p {
    font-size: 14px;
    color: #555;
    line-height: 1.6;
    margin: 0;
    text-align: left;
  }

  .copyright {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid rgba(232, 232, 232, 0.5);
  }

  .copyright p {
    font-size: 12px;
    color: #999;
    margin: 0;
  }

  /* 暗色模式适配 */
  @media (prefers-color-scheme: dark) {
    .mobile-person-center {
      background-color: #1a1a1a;
    }

    .user-header {
      &::before {
        background: rgba(0, 0, 0, 0.5);
      }
    }

    .menu-list {
      background: #2a2a2a;
    }

    .menu-item {
      border-bottom-color: #3a3a3a;

      &:hover {
        background-color: #3a3a3a;
      }

      &:active {
        background-color: #4a4a4a;
      }
    }

    .menu-icon-wrapper {
      background-color: #3a3a3a;
    }

    .menu-title {
      color: #fff;
    }

    .menu-desc {
      color: #ccc;
    }

    .menu-arrow {
      color: #666;
    }

    /* 关于页面暗色模式 */
    .about-background {
      opacity: 0.02;
    }

    .system-title {
      color: #fff;
    }

    .form-logo {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .system-details {
      background: rgba(58, 58, 58, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .detail-item {
      border-bottom-color: #4a4a4a;
    }

    .detail-label {
      color: #ccc;
    }

    .detail-value {
      color: #fff;
    }

    .system-description {
      background: linear-gradient(135deg, rgba(42, 74, 90, 0.9), rgba(26, 58, 74, 0.9));
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .system-description p {
      color: #ddd;
    }

    .copyright {
      border-top-color: rgba(74, 74, 74, 0.5);
    }

    .copyright p {
      color: #999;
    }
  }
</style>
