<template>
  <!-- 移动端使用专用组件 -->
  <MobileTaskList
    v-if="isMobileDevice"
    :process-instance="processInstance"
    :tasks="tasks"
    :running-tasks="runningTasks"
    @open-children-task="openChildrenTask"
    @handle-form-detail="handleFormDetail"
    @handle-time-edit="handleTimeEdit"
    @handle-attachment="handleAttachmentClick"
  />

  <!-- PC端保持原有布局 -->
  <el-card v-else v-loading="loading" class="box-card" shadow="hover">
    <template #header>
      <span style="font-size: 20.8px; display: flex; align-items: center">
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
          <g
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            color="currentColor"
          >
            <path
              d="M17 2v2m-5-2v2M7 2v2m-3.5 6c0-3.3 0-4.95 1.025-5.975S7.2 3 10.5 3h3c3.3 0 4.95 0 5.975 1.025S20.5 6.7 20.5 10v5c0 3.3 0 4.95-1.025 5.975S16.8 22 13.5 22h-3c-3.3 0-4.95 0-5.975-1.025S3.5 18.3 3.5 15zm10 6H17m-3.5-7H17"
            />
            <path d="M7 10s.5 0 1 1c0 0 1.588-2.5 3-3m-4 9s.5 0 1 1c0 0 1.588-2.5 3-3" />
          </g>
        </svg>
        <span style="margin-left: 5px">审批记录</span>
      </span>
    </template>
    <!-- PC端保持原有布局 -->
    <el-col :offset="3" :span="17">
      <div class="block">
        <el-timeline>
          <el-timeline-item v-if="processInstance.endTime" :type="getProcessInstanceTimelineItemType(processInstance)">
            <p style="font-weight: 700">
              结束流程：在 {{ formatDate(processInstance?.endTime) }} 结束
              <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="processInstance.status" />
            </p>
          </el-timeline-item>

          <el-timeline-item
            v-for="(item, index) in tasks"
            :key="index"
            size="large"
            :icon="getTaskTimelineItemIcon()"
            :type="getTaskTimelineItemType(item)"
            style="margin-bottom: 10px"
          >
            <!-- {{ item }} -->
            <p style="font-weight: 700; margin-bottom: 5px">
              审批任务：{{ item.name }}
              <dict-tag
                :type="DICT_TYPE.BPM_TASK_STATUS"
                :value="item.status"
                style="margin-left: 10px; margin-bottom: 1px"
              />
              <!-- {{ item.children }} -->
              <el-button
                class="ml-10px"
                v-if="item.children"
                @click="openChildrenTask(item)"
                size="small"
                style="margin-bottom: 5px"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"
                  />
                </svg>
                子任务
              </el-button>
              <!-- item.formId: {{ item.formId }} -->
              <el-button
                class="ml-10px"
                size="small"
                v-if="item.formId > 0"
                @click="handleFormDetail(item)"
                style="margin-bottom: 5px"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"
                  />
                </svg>
                查看表单
              </el-button>
            </p>

            <el-card :body-style="{ padding: '10px' }" style="font-size: 1em">
              <span v-if="item.assigneeUser" style="margin-right: 30px; font-weight: normal">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                  />
                </svg>
                审批人：{{ `${item.assigneeUser.empName}(${item.assigneeUser.empCode})` }}

                <n-tag style="margin-left: 10px" type="info">{{ item.assigneeUser.deptName }}</n-tag>
              </span>
              <!--              <label v-if="item.createTime" style="font-weight: normal">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"
                  />
                </svg>
                开始时间：</label
              >
              <label style="font-weight: normal; color: #8a909c">
                {{ formatDate(item?.createTime) }}
              </label>-->
              <label v-if="item.endTime" style="margin-left: 30px; font-weight: normal"> 审批时间： </label>
              <el-button
                v-if="item.endTime"
                :disabled="runningTasks.length == 0"
                @click="
                  showChangeTime(time => {
                    let data = {
                      processInstanceId: item.processInstanceId,
                      taskId: item.id,
                      auditTime: time,
                    }
                    TaskApi.updTaskAuditTime(data).then(() => {
                      item.auditTime = time
                    })
                    console.log('@@-curItem', item)
                  })
                "
              >
                {{ formatDate(item?.auditTime ?? item?.endTime) }}
              </el-button>
              <!--              <label v-if="item.durationInMillis" style="margin-left: 30px; font-weight: normal">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M15 1H9v2h6V1zm-4 13h2V8h-2v6zm8.03-6.61l1.42-1.42c-.43-.51-.9-.99-1.41-1.41l-1.42 1.42C16.07 4.74 14.12 4 12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9 9-4.03 9-9c0-2.12-.74-4.07-1.97-5.61zM12 20c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"
                  />
                </svg>
                耗时：
              </label>
              <label v-if="item.durationInMillis" style="font-weight: normal; color: #8a909c">
                {{ formatPast2(item?.durationInMillis) }}
              </label>-->
              <p v-if="item.reason" style="margin-top: 10px">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M20.41 4.94l-1.35-1.35c-.78-.78-2.05-.78-2.83 0L3 16.82V21h4.18L20.41 7.77c.79-.78.79-2.05 0-2.83zm-14 14.12L5 19v-1.36l9.82-9.82 1.41 1.41-9.82 9.83z"
                  />
                </svg>
                审批建议：{{ item.reason }}
              </p>
              <!-- 签名 -->
              <n-space
                style="margin-top: 10px; font-weight: normal"
                v-if="item.needSign && item.status != 1 && item.signUrl"
              >
                <span style="font-weight: normal">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    width="1em"
                    height="1em"
                    fill="currentColor"
                  >
                    <path
                      d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04a.996.996 0 0 0 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
                    />
                  </svg>
                  签名：</span
                >
                <!-- {{ JPGlobal.getRealOCUrl(item.signUrl) }} -->
                <!-- <n-image :width="60" v-if="curSignStep.sign" :src="curSignStep.sign" :preview-disabled="true" /> -->
                <n-image :width="60" :src="JPGlobal.getRealOCUrl(item.signUrl)" :preview-disabled="false" />
              </n-space>

              <el-card style="margin-top: 10px; font-weight: normal" v-if="item.needAttachment && item.status != 1">
                <span>
                  <Files style="width: 1.1em; height: 1.1em; margin-right: 2px" />
                  附件：
                </span>
                <!-- 附件 -->
                <ul v-if="item.attachmentList && item.attachmentList.length > 0">
                  <li v-for="attachment in item.attachmentList" :key="attachment.value" class="attachmentList">
                    <el-link
                      @click="
                        changeAttachmentOption(attachment, percent => {
                          attachment.percent = percent
                        })
                      "
                    >
                      {{ attachment.label }}
                    </el-link>
                    <el-tooltip content="下载" placement="top" :enterable="false">
                      <n-button class="hoverShow" style="margin-top: -5px" @click="downloadAttachment(attachment)">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          width="1.1em"
                          height="1.1em"
                          fill="currentColor"
                        >
                          <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
                        </svg>
                      </n-button>
                    </el-tooltip>
                    <el-progress v-if="attachment.percent" :percentage="attachment.percent" />
                  </li>
                </ul>
              </el-card>
              <!-- {{ curAttachmentStepOption }} -->
              <!-- { "label": "file.pdf", "fileUrl": "http://vmm:9000/bpm/audit/att/H02NSS3N5R6EE62428C0863B13.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=b5VNfBMYZnBsFIXVm7XN%2F20240820%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20240820T054915Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=74d1136fc36ee4fcff40ef672edd2e532070ca690a4c84f73a002494ae602d3a", "value": "http://vmm:9000/bpm/audit/att/H02NSS3N5R6EE62428C0863B13.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=b5VNfBMYZnBsFIXVm7XN%2F20240820%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20240820T054915Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=74d1136fc36ee4fcff40ef672edd2e532070ca690a4c84f73a002494ae602d3a", "type": "pdf" } -->
            </el-card>
          </el-timeline-item>
          <el-timeline-item type="success">
            <p style="font-weight: 700">
              <!-- {{ processInstance }} -->
              发起流程：【{{ processInstance.startUser?.empName }}】在
              {{ formatDate(processInstance?.startTime) }} 发起【 {{ processInstance.name }} 】流程
            </p>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-col>
  </el-card>
  <!-- 弹窗：附件预览 -->
  <j-preview
    v-model:show="showAttachmentPreview"
    :url="String(curAttachmentStepOption.value)"
    :type="curAttachmentStepOption.type as PreviewType"
    style="z-index: 2100"
  />
  <!-- 弹窗：子任务  -->
  <TaskSignList ref="taskSignListRef" @success="refresh" />
  <!-- 弹窗：表单 -->
  <Dialog title="表单详情" v-model="taskFormVisible" :width="formDetailsWidth">
    <!-- {{ taskForm }} -->
    <form-create ref="fApi" v-model="taskForm.value" :option="taskForm.option" :rule="taskForm.rule" disabled />
  </Dialog>
  
  <!-- 弹窗：修改时间 -->
  <n-modal v-model:show="showAuditTime" :mask-closable="false">
    <n-card
      :style="isMobileDevice ? 'width: 95%; margin: 10px;' : 'width: 450px'"
      title="修改审批时间"
      :bordered="false"
      role="dialog"
      aria-modal="true"
      :class="{ 'mobile-time-modal': isMobileDevice }"
    >
      <n-date-picker
        v-model:value="auditTime"
        placeholder="请选择时间"
        type="datetime"
        :class="isMobileDevice ? 'w-full' : ''"
      />
      <template #footer>
        <div :class="isMobileDevice ? 'flex flex-col space-y-2' : 'upload-footer-buttons'">
          <n-button type="primary" @click="changeAuditTime" :class="isMobileDevice ? 'w-full' : ''"> 确认 </n-button>
          <n-button type="info" @click="showAuditTime = false" :class="isMobileDevice ? 'w-full' : ''">
            取消
          </n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>
<script lang="ts" setup>
import { Dialog } from '@/components/common/bpm/Dialog'
import { Files } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/bpmAdapter/formatTime'
import { propTypes } from '@/utils/bpmAdapter/propTypes'
import { DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
import TaskSignList from './dialog/TaskSignList.vue'
import MobileTaskList from './components/MobileTaskList.vue'
import type { ApiAttrs } from '@form-create/element-ui/types/config'
import { setConfAndFields2 } from '@/utils/bpmAdapter/formCreate'
import { ref, nextTick, inject, onMounted, Ref } from 'vue'
import { useMessage } from '@/components/common/bpm/bpmAdapter/useMessage'
import JPGlobal from '@/types/common/jglobal'
import { Option, PreviewType } from '@jtypes'
import { NTag, SelectOption } from 'naive-ui'
import { DeviceType, getScreenInfo } from '@/utils/device'

defineOptions({ name: 'BpmProcessInstanceTaskList' })

// 移动端检测
const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')
const screenInfo = ref<{
  width: number
  height: number
  deviceType: DeviceType
}>()
const formDetailsWidth = ref(0)
onMounted(() => {
  screenInfo.value = getScreenInfo()
  if (isMobileDevice) {
    formDetailsWidth.value = screenInfo.value.width
  } else {
    formDetailsWidth.value = 600
  }
})
defineProps({
  loading: propTypes.bool, // 是否加载中
  processInstance: propTypes.object, // 流程实例
  tasks: propTypes.arrayOf(propTypes.object), // 流程任务的数组
  runningTasks: propTypes.arrayOf(propTypes.object),
})

/** 获得流程实例对应的颜色 */
const getProcessInstanceTimelineItemType = (item: any) => {
  if (item.status === 2) {
    return 'success'
  }
  if (item.status === 3) {
    return 'danger'
  }
  if (item.status === 4) {
    return 'warning'
  }
  return 'primary'
}

/** 获得任务对应的颜色 */
const getTaskTimelineItemType = (item: any) => {
  if ([0, 1, 6, 7].includes(item.status)) {
    return 'primary'
  }
  if (item.status === 2) {
    return 'success'
  }
  if (item.status === 3) {
    return 'danger'
  }
  if (item.status === 4) {
    return 'info'
  }
  if (item.status === 5) {
    return 'warning'
  }
  return 'primary'
}

import { User } from '@element-plus/icons-vue'
import * as TaskApi from '@/api/bpm/task'

const getTaskTimelineItemIcon = () => {
  return User
}

/** 子任务 */
const taskSignListRef = ref()
const openChildrenTask = (item: any) => {
  taskSignListRef.value.open(item)
}

/** 移动端事件处理 */
const handleTimeEdit = (item: any) => {
  showChangeTime(time => {
    let data = {
      processInstanceId: item.processInstanceId,
      taskId: item.id,
      auditTime: time,
    }
    TaskApi.updTaskAuditTime(data).then(() => {
      item.auditTime = time
    })
  })
}

const handleAttachmentClick = (attachment: any) => {
  changeAttachmentOption(attachment, (percent: number) => {
    attachment.percent = percent
  })
}

/** 查看表单 */
const fApi = ref<ApiAttrs>() // form-create 的 API 操作类
const taskForm = ref({
  rule: [],
  option: {
    submitBtn: false,
  },
  value: {},
}) // 流程任务的表单详情
const taskFormVisible = ref(false)
const handleFormDetail = async row => {
  // 设置表单
  setConfAndFields2(taskForm, row.formConf, row.formFields, row.formVariables, false)
  // 弹窗打开
  taskFormVisible.value = true
  // 隐藏提交、重置按钮，设置禁用只读
  await nextTick()
  //@ts-ignore
  fApi.value.fapi.btn.show(false)
  //@ts-ignore
  fApi.value?.fapi?.resetBtn.show(false)
  //@ts-ignore
  fApi.value?.fapi?.disabled(true)
}

/** 刷新数据 */
const emit = defineEmits(['refresh']) // 定义 success 事件，用于操作成功后的回调
const refresh = () => {
  emit('refresh')
}

// 点击确认

let curAuditTimeCallBack: Function = () => {}
const showAttachmentPreview = ref(false)
const showAuditTime = ref(false)
const auditTime = ref()
const curAttachmentStepOption = ref<SelectOption>({})

const changeAttachmentOption = async (option: Option, callback: (percent: number) => void) => {
  const temp = option.label.split('.')
  const type = temp[temp.length - 1]
  option.type = type
  if (['png', 'jpg', 'jpeg', 'bmp', 'gif'].includes(type)) {
    option.type = 'image'
    curAttachmentStepOption.value = option

    setTimeout(() => {
      showAttachmentPreview.value = true
    }, 100)
    return
  }

  if (['png', 'jpg', 'jpeg', 'bmp', 'gif', 'pdf'].includes(type)) {
    setTimeout(() => {
      curAttachmentStepOption.value = option

      showAttachmentPreview.value = true
    }, 100)
  } else {
    downloadWithProgress(option.fileUrl, option.label, callback)
  }
}
const downloadAttachment = (attachment: any) => {
  downloadWithProgress(attachment.fileUrl, attachment.label, (percent: number) => {
    attachment.percent = percent
  })
}

//修改审批时间
const showChangeTime = callBack => {
  curAuditTimeCallBack = callBack
  showAuditTime.value = true
}

const changeAuditTime = () => {
  if (!auditTime.value) {
    useMessage().error('请选择审批时间')
    return
  }
  
  // 发送请求，触发时间改变
  curAuditTimeCallBack(auditTime.value)
  auditTime.value = null
  showAuditTime.value = false
  useMessage().success('审批时间修改成功')
}

const attachmentCache = new Map<string, Blob>()

function downloadWithProgress(URL: string, FILE_NAME: string, callback: (percent: number) => void) {
  useMessage().success('下载中 请稍等。')
  //缓存附件 多次点击无需重复下载
  if (attachmentCache.has(URL)) {
    const blob = attachmentCache.get(URL) as Blob
    const anchor = document.createElement('a')
    anchor.href = window.URL.createObjectURL(blob)
    anchor.download = FILE_NAME
    document.body.appendChild(anchor)
    anchor.click()
    return
  }
  const startTime = new Date().getTime()

  const request = new XMLHttpRequest()

  request.responseType = 'blob'
  request.open('get', URL, true)
  request.send()

  request.onreadystatechange = function () {
    if (this.readyState === 4 && this.status === 200) {
      const imageURL = window.URL.createObjectURL(this.response)
      attachmentCache.set(URL, this.response)
      const anchor = document.createElement('a')
      anchor.href = imageURL
      anchor.download = FILE_NAME
      document.body.appendChild(anchor)
      anchor.click()
    }
  }

  request.onprogress = function (e) {
    const percent_complete = Math.floor((e.loaded / e.total) * 100)

    const duration = (new Date().getTime() - startTime) / 1000
    const bps = e.loaded / duration

    const kbps = Math.floor(bps / 1024)

    const time = (e.total - e.loaded) / bps
    const seconds = Math.floor(time % 60)
    const minutes = Math.floor(time / 60)
    callback(percent_complete)
    console.log(`${percent_complete}% - ${kbps} Kbps - ${minutes} min ${seconds} sec remaining`)
  }
}
</script>

<style scoped>
:deep(.el-timeline-item__node--large) {
  width: 25px !important;
  height: 25px !important;
  margin-left: -5px;
}
:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
}
.attachmentList:hover .hoverShow {
  display: inline;
}
.hoverShow {
  display: none;
}
.upload-footer-buttons {
  width: 100%;
  display: flex;
  justify-content: flex-end;

  & > * {
    margin-left: 0.5rem;
  }
}

/* 移动端时间弹窗样式 */
.mobile-time-modal :deep(.n-card-header) {
  padding: 16px 16px 8px 16px;
  font-size: 16px;
}

.mobile-time-modal :deep(.n-card__content) {
  padding: 8px 16px 16px 16px;
}

.mobile-time-modal :deep(.n-card__footer) {
  padding: 8px 16px 16px 16px;
}

@media (max-width: 768px) {
  .mobile-time-modal :deep(.n-date-picker) {
    width: 100% !important;
  }
}

/* PC端样式保持不变 */
</style>
